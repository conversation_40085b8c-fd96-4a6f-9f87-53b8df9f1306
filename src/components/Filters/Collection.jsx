import React, { useEffect, useState, useLayoutEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  TouchableWithoutFeedback,
  Animated,
  Easing,
} from 'react-native';
import axios from 'axios';
import Filter from './Filter';
import ListingCards from '../../screens/ListingCards/ListingCards';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import CheckBox from '@react-native-community/checkbox';
import { styles } from './collectionStyles';
const remove = require('../../assets/cross.png');
const filterIcon = require('../../assets/filter.png');
const sortIcon = require('../../assets/sort.png');

const Collection = ({ navigation, route }) => {
  // Read q, lat, long from route.params for search and location
  const initialSearchParam = route?.params?.q ?? '';
  const initialLat = route?.params?.lat ?? null;
  const initialLong = route?.params?.long ?? null;

  const [filterVisible, setFilterVisible] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({});
  const [filterUrl, setFilterUrl] = useState('');
  const [searchParam, setSearchParam] = useState(initialSearchParam);
  const [firstParam, setFirstParam] = useState('');
  const [categories, setCategories] = useState([]);
  const [sortVisible, setSortVisible] = useState(false);
  const [sortModalVisible, setSortModalVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState('');
  const [selectedSortValue, setSelectedSortValue] = useState('');
  const [selectedSortLabel, setSelectedSortLabel] = useState('Sort');
  const sortAnim = useRef(new Animated.Value(0)).current;
  const [userLatitude, setUserLatitude] = useState(initialLat);
  const [userLongitude, setUserLongitude] = useState(initialLong);
  const [courses, setCourses] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch courses with filters, search, sort, and pagination
  const fetchCourses = async (reset = false) => {
    setIsLoading(true);
    let params = {};
    params.page = page;
    if (searchParam) params.q = searchParam.trim();
    if (firstParam) params.category = firstParam;
    if (selectedFilters) {
      Object.entries(selectedFilters).forEach(([key, values]) => {
        if (key === 'priceRange' && Array.isArray(values)) {
          const minPrice = Math.min(...values.map((range) => range[0]));
          const maxPrice = Math.max(...values.map((range) => range[1]));
          params.min = minPrice;
          params.max = maxPrice;
        } else if (Array.isArray(values)) {
          if (values.length > 0) params[key] = values;
        } else if (values) {
          params[key] = values;
        }
      });
    }
    if (userLatitude && userLongitude) {
      params.lat = userLatitude;
      params.long = userLongitude;
    }
    if (selectedSort && selectedSortValue) {
      params[selectedSort] = selectedSortValue;
    }
    const url = `${NEXT_PUBLIC_BASE_URL}/api/course/filter?${new URLSearchParams(params).toString()}`;
    try {
      const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/course/filter`, { params });
      const newCourses = response.data?.coursesAndCoaches?.filter(
        (course) => course?.courseName && course.courseName.trim().toLowerCase() !== 'no course'
      );
      setHasMore(newCourses.length > 0);
      setCourses((prev) => (reset ? newCourses : [...prev, ...newCourses]));
    } catch (error) {
      console.error('Error fetching courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setCourses([]);
  }, [firstParam, searchParam]);

  useEffect(() => {
    if (firstParam || searchParam) {
      fetchCourses(page === 1);
    }
  }, [page, selectedFilters, firstParam, searchParam, userLatitude, userLongitude, selectedSort, selectedSortValue]);

  useEffect(() => {
    setPage(1);
  }, [selectedFilters, firstParam, searchParam, userLatitude, userLongitude, selectedSort, selectedSortValue]);

  const handleLoadMore = () => {
    if (hasMore && !isLoading) setPage((prev) => prev + 1);
  };

  const handleRefresh = () => {
    setPage(1);
    fetchCourses(true);
  };

  const sortingOptions = [
    { id: 'ratings', name: 'Best Rating', value: 'des' },
    { id: 'createdAt', name: 'Newest', value: 'asc' },
    { id: 'fees', name: 'Price: Low to High', value: 'asc' },
    { id: 'fees', name: 'Price: High to Low', value: 'des' },
  ];

  const openSortModal = () => {
    setSortModalVisible(true);
    Animated.timing(sortAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
      easing: Easing.out(Easing.ease),
    }).start();
  };

  const closeSortModal = () => {
    Animated.timing(sortAnim, {
      toValue: 0,
      duration: 250,
      useNativeDriver: true,
      easing: Easing.in(Easing.ease),
    }).start(() => {
      setSortModalVisible(false);
      setSortVisible(false);
    });
  };

  useEffect(() => {
    if (sortVisible) {
      openSortModal();
    } else if (sortModalVisible) {
      closeSortModal();
    }
  }, [sortVisible]);

  useEffect(() => {
    const fetchAndSetCategories = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/category`);
        const data = response.data.data;
        const serverCategories = data.map((category) => {
          return category.lowerCaseName;
        });
        setCategories(serverCategories);
      } catch (error) {
        console.error('Error fetching categories: 52');
      }
    };
    fetchAndSetCategories();
  }, []);

  const handleSortSelect = (itemId, itemValue) => {
    if (selectedSort === itemId && selectedSortValue === itemValue) {
      setSelectedSort('');
      setSelectedSortValue('');
    } else {
      setSelectedSort(itemId);
      setSelectedSortValue(itemValue);
    }
    setSortVisible(false);
  };

  const applyFilter = (selectedOptions) => {
    setFilterVisible(false);
    setSelectedFilters(selectedOptions);
  };

  useEffect(() => {
    if (route.params?.selectedOption) {
      const newSelectedOption = route.params.selectedOption.toLowerCase();
      setFirstParam(newSelectedOption);
      setSelectedFilters((currentFilters) => ({
        ...currentFilters,
        category: [newSelectedOption],
      }));
    } else if (route.params?.searchQuery) {
      const searchTerm = route.params.searchQuery.toLowerCase();
      setSearchParam(searchTerm);
      const matchedFilter = matchSearchQueryWithFilters(searchTerm);
      if (matchedFilter) {
        setSelectedFilters((currentFilters) => ({
          ...currentFilters,
          category: [matchedFilter],
        }));
      }
    }
  }, [route.params?.selectedOption, route.params?.searchQuery, categories]);

  const matchSearchQueryWithFilters = (query) => {
    const queryLower = query.toLowerCase();
    const matchedCategory = categories.find((category) => category.includes(queryLower));
    return matchedCategory;
  };

  const renderTags = () => {
    if (!selectedFilters) return null;
    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {Object.entries(selectedFilters).map(([key, values]) =>
          values.map((value, index) => {
            if (key === 'priceRange') {
              const [min, max] = value;
              return (
                <TouchableOpacity
                  key={`${key}-${min}-${max}`}
                  style={styles.tag}
                  onPress={() => removeTags(key, index)}
                >
                  <Text style={styles.tagText}>{`₹${min} - ₹${max}`}</Text>
                  <TouchableOpacity onPress={() => removeTags(key, index)}>
                    <Image source={remove} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </TouchableOpacity>
              );
            } else {
              return (
                <TouchableOpacity
                  key={`${key}-${value}`}
                  style={styles.tag}
                  onPress={() => removeTags(key, index)}
                >
                  <Text style={styles.tagText}>{value}</Text>
                  <TouchableOpacity onPress={() => removeTags(key, index)}>
                    <Image source={remove} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </TouchableOpacity>
              );
            }
          })
        )}
      </ScrollView>
    );
  };

  const removeTags = (filterId, index) => {
    const updatedFilters = { ...selectedFilters };
    if (Array.isArray(updatedFilters[filterId])) {
      updatedFilters[filterId].splice(index, 1);
      if (updatedFilters[filterId].length === 0) {
        delete updatedFilters[filterId];
      }
    } else {
      delete updatedFilters[filterId];
    }
    setSelectedFilters(updatedFilters);
  };

  useLayoutEffect(() => {
    const searchQueryValue = route.params.searchQueryValue;
    const headerTitle =
      (firstParam && searchQueryValue) ||
      firstParam ||
      searchQueryValue ||
      'collection';
    navigation.setOptions({
      headerTitle: headerTitle,
    });
  }, [firstParam, navigation, searchParam]);

  useEffect(() => {
    if (route.params?.lat && route.params?.long) {
      setUserLatitude(route.params.lat);
      setUserLongitude(route.params.long);
      console.log(
        'Collection: Using coordinates from route params:',
        route.params.lati,
        route.params.long
      );
    } else {
      setUserLatitude(null);
      setUserLongitude(null);
      console.log('Collection: No coordinates provided in route params.');
    }
  }, [route.params?.latitude, route.params?.longitude]);

  // Notify Me handler moved here so it can access firstParam, searchParam, filterUrl
  const handleNotifyMe = async (email, setEmailError, setNotifySuccess) => {
    console.log('handleNotifyMe called with:', { email });
    if (!emailRegExp.test(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    setEmailError("");
    let message = "";
    if (firstParam) message += `Category: ${firstParam} `;
    if (searchParam) message += `Search: ${searchParam} `;
    if (filterUrl) message += `Filter: ${filterUrl}`;
    console.log('handleNotifyMe message:', message.trim());
    try {
      const response = await axios.post(`${NEXT_PUBLIC_BASE_URL}/api/courseNotify`, {
        email,
        message: message.trim(),
      });
      console.log('handleNotifyMe API response:', response);
      if (response.status === 200) setNotifySuccess(true);
    } catch (error) {
      console.error("Error sending notification request:", error);
    }
  };

  return (
    <View style={styles.container}>
      {/* <Search /> */}
      <View style={styles.buttonsContainer}>
        <TouchableOpacity
          onPress={() => setFilterVisible(true)}
          style={styles.filterButton}
        >
          <Image source={filterIcon} style={{ width: 20, height: 20, marginRight: 10 }} />
          <Text style={styles.buttonText}>Filters</Text>
        </TouchableOpacity>
        <View style={styles.divider} />
        <TouchableOpacity
          onPress={() => setSortVisible(true)}
          style={styles.filterButton}
        >
          <Image source={sortIcon} style={{ width: 20, height: 20, marginRight: 10 }} />
          <Text style={styles.buttonText}>{selectedSortLabel}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.horizontalLine} />
      <Modal
        animationType="none"
        transparent={true}
        visible={sortModalVisible}
        onRequestClose={closeSortModal}
      >
        <TouchableWithoutFeedback onPress={closeSortModal}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles.modalView,
                  {
                    opacity: sortAnim,
                    transform: [
                      {
                        translateY: sortAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [100, 0],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <Text style={styles.modalTitle}>Sort Options</Text>
                <ScrollView>
                  {sortingOptions.map((option) => (
                    <View key={option.id} style={styles.sortOption}>
                      <TouchableOpacity
                        onPress={() => handleSortSelect(option.id, option.value)}
                        style={styles.labelWrapper}
                      >
                        <Text style={styles.sortOptionText}>{option.name}</Text>
                      </TouchableOpacity>
                      <CheckBox
                        value={selectedSort === option.id && selectedSortValue === option.value}
                        onValueChange={() => handleSortSelect(option.id, option.value)}
                        tintColors={{ true: '#007AFF', false: '#000' }}
                      />
                    </View>
                  ))}
                </ScrollView>
                <TouchableOpacity style={styles.cancelButton} onPress={closeSortModal}>
                  <Text style={styles.cancelButtonText}>CANCEL</Text>
                </TouchableOpacity>
              </Animated.View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
        <View style={styles.filterTagsContainer}></View>
      </Modal>
      <Filter
        visible={filterVisible}
        onClose={() => setFilterVisible(false)}
        applyFilter={applyFilter}
        selectedFilters={selectedFilters}
      />
      <View style={styles.tagcontainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderTags()}
        </ScrollView>
      </View>
      <View style={styles.listingCardsContainer}>
        <ListingCards
          courses={courses}
          isLoading={isLoading}
          hasMore={hasMore}
          onEndReached={handleLoadMore}
          onRefresh={handleRefresh}
          handleNotifyMe={handleNotifyMe} // pass as prop
        />
      </View>
    </View>
  );
};

export default Collection;